// app/providers.tsx
'use client';

import { ReactNode } from "react";
import { AuthProvider } from "../contexts/AuthContext";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { AnalyticsProvider } from "lib/analytics/analyticsProvider";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <SelectedDocProvider>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </SelectedDocProvider>
    </AuthProvider>
  );
}