# Production Environment Variables for CastMate
NEXTAUTH_URL="https://www.castmate-ai.com"
NEXTAUTH_SECRET="indefSECRET"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="1011758931813-fcqukdijh6rkqfp1nb4ruac6udnptlag.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Database and API Configuration
PINECONE_INDEX="ikedia" 
PINECONE_scenemate_INDEX="scenemate"
NEXT_PUBLIC_PINECONE_INDEX="ikedia" 
PINECONE_ENVIRONMENT="us-east-1"
PINECONE_API_KEY="3b176b1d-1ed2-437b-8346-0f497c138048"

# AI API Keys
OPENAI_API_KEY="***********************************************************************************************"
OPENAI_API_REHEARSAL_KEY="********************************************************************************************************************************************************************"
GROQ_API_KEY="********************************************************"
GROQ_ike_FREE_KEY="********************************************************"
COHERE_API_KEY="mxuiqfIyV8MPUMSPJGENx7yELKYfCnspcPqHVTAg"
CLAUDE_API_KEY="************************************************************************************************************"

# Model Configuration
GROQ_MODEL="llama-3.3-70b-versatile"
Deep_Seek_Model="deepseek-r1-distill-llama-70b"
GROQ_VISION_MODEL="llama-3.3-70b-versatile"
GROQ_MODEL_LATEST="llama-3.3-70b-versatile"
OPENAI_MODEL="gpt-4o-2024-11-20"
CLAUDE_MODEL="claude-3-5-sonnet-20240620"
OPENAI_MODEL_ID="gpt-4o-realtime-preview-2024-12-17"

# External Services
SEARCH_API="BSA3h5kStRJvg8hjUWnccgKsKFLlnjB"
FOLLOWUPFLOWISE="https://flowiseai-railway-production-28a57.up.railway.app/api/v1/prediction/********-312c-4853-8dcf-6528a1af07f0"
FLOWISE_SCHEMA="https://flowiseai-railway-production-28a57.up.railway.app/api/v1/prediction/************************************"

# Firebase Configuration
FIREBASE_STORAGE_BUCKET="indef2024-d11b5.appspot.com"

# Application Configuration
NODE_ENV="production"
NEXT_PUBLIC_SERVICE_ACCOUNT="<EMAIL>"
NEXT_PUBLIC_BASE_URL="https://www.castmate-ai.com"
NEXT_PUBLIC_API_URL=""

# Email Configuration
EMAIL_HOST="smtp.hostinger.com"
EMAIL_PORT="587"
EMAIL_USER="<EMAIL>"
EMAIL_PASS="KaiodeIke2#"
EMAIL_FROM="<EMAIL>"

# System Configuration
AI_IMAGE="/favicon_message.png"
SYS_ADMIN="<EMAIL>"

# ElevenLabs Configuration
NEXT_PUBLIC_ELEVENLABS_AGENT_ID="EwkhoLt9gbQa1RhA4G08"
NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID="1WU4LPk9482VXQFb80aq"
NEXT_PUBLIC_ELEVENLABS_API_KEY="***************************************************"
