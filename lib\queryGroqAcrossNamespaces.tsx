import { Pinecone } from "@pinecone-database/pinecone";
import { ChatGroq } from "@langchain/groq";
import {
  SystemMessagePromptTemplate,
  HumanMessagePromptTemplate,
  ChatPromptTemplate,
  BaseMessagePromptTemplate,
  AIMessagePromptTemplate,
} from "@langchain/core/prompts";
import { FirestoreStore } from "./FirestoreStore";
import { DocumentProcessor } from "./optimizedDocumentProcessing";
import { fetchDocumentChunksByChunkIds } from "lib/fetchDocumentChunksByChunkIds";
import { ChatHistoryProcessor, ChatMessage } from './ChatHistoryProcessor';
import { AnalyticsMiddleware } from "./analytics/AnalyticsMiddleware";

const MAX_HISTORY_MESSAGES = 10;
const MAX_TOKENS_PER_MESSAGE = 500;
const SYSTEM_PROMPT_TOKENS = 300;
const MAX_CONTENT_PREVIEW_LENGTH = 200;
const FALLBACK_MESSAGE = "I apologize, but I couldn't find relevant information to answer your question. Please try rephrasing or asking a different question.\nEND_METADATA\n";

interface StreamOptions {
  controller: ReadableStreamDefaultController<any>;
  signal?: AbortSignal;
}

interface MatchMetadata {
  chunkId: string;
  score?: number;
  namespace?: string;
  embedding?: number[];
}

interface QueryMatch {
  metadata?: MatchMetadata;
  score?: number;
  values?: number[];
  id?: string;
}

interface TokenUsage {
  contextTokens: number;
  systemPromptTokens: number;
  chatHistoryTokens: number;
  totalTokens: number;
}

interface PromptParams {
  systemPrompt: SystemMessagePromptTemplate;
  context: string;
  metadata: string;
  chatHistory: ChatMessage[];
  userQuery: string;
}

interface ProcessedMetadata {
  sources: any[];
  totalTokens: number;
  chunkCount: number;
  averageRelevance: number;
  namespaceDistribution?: Record<string, number>;
}

function cleanChunkId(chunkId: string): string {
  const parts = chunkId.split('_');
  if (parts.length > 2) {
    const uniqueParts = [...new Set(parts.slice(0, -1))];
    return `${uniqueParts.join('_')}_${parts[parts.length - 1]}`;
  }
  return chunkId;
}

function initializePinecone() {
  try {
    const pinecone = new Pinecone();
    const index = pinecone.Index(process.env.PINECONE_INDEX!);
    return index;
  } catch (error) {
    console.error("Failed to initialize Pinecone:", error);
    throw new Error("Pinecone initialization failed");
  }
}

async function streamToClient(
  { controller }: StreamOptions,
  chunk: string,
  shouldBuffer: boolean = false
): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const encoder = new TextEncoder();
      const encoded = encoder.encode(chunk);

      if (shouldBuffer) {
        setTimeout(() => {
          controller.enqueue(encoded);
          resolve();
        }, 50);
      } else {
        controller.enqueue(encoded);
        resolve();
      }
    } catch (error) {
      console.error("Streaming error:", error);
      reject(error);
    }
  });
}

function getSystemPrompt() {
  const template = `
You are to assume the role of the author of the Publications or Content. With extensive knowledge and expertise in your field, you are dedicated to informing and assisting users with accurate and relevant information.

**Response Guidelines:**

1. **Focused Expertise:**
- When asked about a particular topic, provide detailed information exclusively from the provided content.
- Avoid including any information not explicitly contained in the content or unrelated information.

2. **Content Integration:**
- Use the content from your publications to enhance and support your responses.
- Ensure that all information shared aligns with your area of expertise.

3. **Chat History Awareness:**
- Consider the full context of the conversation, including previous exchanges.
- Maintain consistency with previous responses while providing new relevant information.

4. **Formatting Standards:**
- Always begin a new paragraph or section with a carriage return and a newline.
- This maintains readability and organization in your responses.
- Add enough space between sections to clearly distinguish items and paragraphs.

5. **If you don't have the answer:**
- Simply tell the user you don't have the answer
- Refer the user to relevant official sources if available from the text or advise the user to reach out to support staff
`;

  return SystemMessagePromptTemplate.fromTemplate(template);
}

async function createPrompt({
  systemPrompt,
  context,
  metadata,
  chatHistory,
  userQuery,
}: PromptParams) {
  try {
    console.log("Creating prompt with:", {
      historyLength: chatHistory.length,
      contextLength: context.length,
      queryLength: userQuery.length
    });

    const messages: BaseMessagePromptTemplate[] = [systemPrompt];
    
    if (chatHistory.length > 0) {
      messages.push(
        SystemMessagePromptTemplate.fromTemplate(
          "\n\nPrevious Conversation:\n{history}\n"
        )
      );
      
      chatHistory.forEach((msg) => {
        const template = msg.role === "user" 
          ? HumanMessagePromptTemplate 
          : AIMessagePromptTemplate;
        messages.push(template.fromTemplate(msg.text));
      });
      
      messages.push(
        SystemMessagePromptTemplate.fromTemplate("\nCurrent Context:\n")
      );
    }
    
    messages.push(
      SystemMessagePromptTemplate.fromTemplate(
        `{context}\n\nMetadata:\n{metadata}`
      )
    );
    
    messages.push(HumanMessagePromptTemplate.fromTemplate("{query}"));

    const promptTemplate = ChatPromptTemplate.fromMessages(messages);
    
    const historyProcessor = new ChatHistoryProcessor();
    const formattedPrompt = await promptTemplate.formatPromptValue({
      history: historyProcessor.formatHistoryForPrompt(chatHistory),
      context,
      metadata,
      query: userQuery,
    });

    return formattedPrompt;
  } catch (error) {
    console.error("Error creating prompt:", error);
    throw new Error("Failed to create prompt template");
  }
}

export async function queryGroqAcrossNamespacesAndProcessAI(
  controller: ReadableStreamDefaultController<any>,
  queryVector: number[],
  namespaces: string[] | null,
  userQuery: string,
  rawChatHistory: string,
  category: string | null,
  userId: string
) {
  const streamOptions: StreamOptions = { controller };
  const documentProcessor = new DocumentProcessor();
  const historyProcessor = new ChatHistoryProcessor({
    maxHistoryMessages: MAX_HISTORY_MESSAGES,
    maxTokensPerMessage: MAX_TOKENS_PER_MESSAGE
  });
  const analytics = new AnalyticsMiddleware();
  
  try {
    const queryId = await analytics.interceptQueryStart(
      userId,
      namespaces,
      userQuery,
      rawChatHistory,
      category
    );

    const { tokenCount: chatHistoryTokens, analytics: historyAnalytics } = 
      historyProcessor.calculateHistoryTokens(rawChatHistory);

    await analytics.interceptChatHistoryMetrics({
      totalMessages: historyAnalytics.totalMessages,
      willBeTruncated: historyAnalytics.willBeTruncated,
      averageMessageLength: historyAnalytics.averageMessageLength,
      originalCount: historyAnalytics.originalCount,
      convertedCount: historyAnalytics.convertedCount,
      truncatedMessages: historyAnalytics.truncatedMessages,
      messagesOverTokenLimit: historyAnalytics.messagesOverTokenLimit
    });      

    await analytics.interceptTokenAnalysis({
      messageCount: historyAnalytics.totalMessages,
      tokenCountPerMessage: historyAnalytics.tokensPerMessage,
      totalTokens: chatHistoryTokens,
      averageTokensPerMessage: chatHistoryTokens / historyAnalytics.totalMessages
    });      
    
    let tokenUsage: TokenUsage = {
      contextTokens: 0,
      systemPromptTokens: SYSTEM_PROMPT_TOKENS,
      chatHistoryTokens,
      totalTokens: SYSTEM_PROMPT_TOKENS + chatHistoryTokens
    };

    if (!Array.isArray(queryVector) || queryVector.length === 0) {
      throw new Error("Invalid query vector");
    }

    if (!userId) {
      throw new Error("User ID is required");
    }

    const pineconeIndex = initializePinecone();
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteCollection });

    const queryResults = await Promise.all(
      (namespaces || []).map(async (namespace) => {
        try {
          const queryResponse = await pineconeIndex.namespace(namespace).query({
            vector: queryVector,
            topK: 5,
            includeValues: true,
            includeMetadata: true,
          });
          
          const matches = queryResponse.matches || [];
          return matches.map(match => ({
            ...match,
            metadata: {
              ...match.metadata,
              chunkId: cleanChunkId(match.metadata?.chunkId ? match.metadata.chunkId.toString() : `${namespace}_${match.id}`)
            }
          })) as QueryMatch[];
        } catch (error) {
          console.error(`Error querying namespace ${namespace}:`, error);
          return [];
        }
      })
    );

    const allMetadata = queryResults
      .flat()
      .map((match) => match.metadata)
      .filter((metadata): metadata is MatchMetadata => !!metadata);

    await analytics.interceptQueryEnd(
      allMetadata.length,
      new Set(allMetadata.map(m => m.namespace)).size
    );      

    const uniqueChunkIds = [...new Set(allMetadata.map((metadata) => metadata.chunkId))];

    if (uniqueChunkIds.length === 0) {
      await streamToClient(streamOptions, FALLBACK_MESSAGE);
      controller.close();
      return;
    }

    const documentChunks = await fetchDocumentChunksByChunkIds(
      uniqueChunkIds,
      firestoreStore
    );

    if (documentChunks.length === 0) {
      await streamToClient(streamOptions, FALLBACK_MESSAGE);
      controller.close();
      return;
    }

    const enrichedChunksPromises = documentChunks.map(async doc => {
      const matchingPineconeData = queryResults.flat().find(
        match => cleanChunkId(match.metadata?.chunkId!) === doc.metadata.chunk_id
      );

      return {
        ...doc,
        values: matchingPineconeData?.values,
        metadata: {
          ...doc.metadata,
          namespace: matchingPineconeData?.metadata?.namespace
        }
      };
    });

    const enrichedChunks = await Promise.all(enrichedChunksPromises);

    const { content: optimizedContent, metadata: processedMetadata } = 
      await documentProcessor.selectRelevantChunks(
        enrichedChunks,
        queryVector,
        {
          maxInputTokens: 2800,
          systemPromptTokens: tokenUsage.systemPromptTokens,
          chatHistoryTokens: tokenUsage.chatHistoryTokens
        }
      );   

    tokenUsage = {
      ...tokenUsage,
      contextTokens: processedMetadata.totalTokens,
      totalTokens: processedMetadata.totalTokens + tokenUsage.systemPromptTokens + tokenUsage.chatHistoryTokens
    };

    await analytics.interceptContentProcessing(
      processedMetadata.totalTokens,
      processedMetadata.chunkCount,
      processedMetadata.averageRelevance,
      processedMetadata.namespaceDistribution,
      tokenUsage.systemPromptTokens,
      tokenUsage.chatHistoryTokens
    );    

    const metadataPayload = JSON.stringify({
      pageContent: optimizedContent.substring(0, MAX_CONTENT_PREVIEW_LENGTH) + "...",
      pageNumber: processedMetadata.sources.map(s => `${s.title} (Page ${s.page})`).join(", "),
      pageTitle: [...new Set(processedMetadata.sources.map(s => s.title))].join("\n"),
      tokenUsage: {
        contextTokens: tokenUsage.contextTokens,
        chatHistoryTokens: tokenUsage.chatHistoryTokens,
        totalChunks: processedMetadata.chunkCount,
        averageRelevance: processedMetadata.averageRelevance.toFixed(2)
      }
    }, null, 2);

    await streamToClient(streamOptions, metadataPayload, true);

    const model = new ChatGroq({
      streaming: true,
      temperature: 0.1,
      model: process.env.GROQ_MODEL!,
      apiKey: process.env.GROQ_API_KEY!,
      maxTokens: 3000,
      callbacks: [
        {
          handleLLMNewToken(token: string) {
            if (controller) {
              controller.enqueue(new TextEncoder().encode(token));
            }
          },
          handleLLMEnd() {
            setTimeout(() => {
              if (controller) {
                controller.close();
              }
            }, 500);
          },
          handleLLMError(err: Error) {
            console.error("LLM Error:", err);
            controller.error(err);
          },
        },
      ],
    });

    const { messages: chatHistoryArray, metrics: historyMetrics } = 
      historyProcessor.convertToMessageArray(rawChatHistory);
    
    const prompt = await createPrompt({
      systemPrompt: getSystemPrompt(),
      context: optimizedContent,
      metadata: JSON.stringify({
        sources: processedMetadata.sources,
        tokenUsage,
        relevance: processedMetadata.averageRelevance
      }, null, 2),
      chatHistory: chatHistoryArray,
      userQuery,
    });

    await model.generatePrompt([prompt]);

  } catch (error) {
    console.error("Error in query processing:", error);
    await streamToClient(streamOptions, FALLBACK_MESSAGE);
    controller.close();
  }
}