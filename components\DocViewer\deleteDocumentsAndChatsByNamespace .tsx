import { adminDb, adminStorage } from 'components/firebase-admin';
import { pineconeClient } from 'lib/pinecone-client';

export const deleteDocumentAndChatsByNamespace = async (userId: string, namespace: string) => {
  try {
    // 1. Delete the document metadata from Firestore (user's files collection)
    const filesRef = adminDb.collection('users').doc(userId).collection('files');
    const filesQuery = filesRef.where('namespace', '==', namespace);
    const querySnapshot = await filesQuery.get();

    if (querySnapshot.empty) {
      throw new Error(`No document found with namespace: ${namespace}`);
    }

    let fileDocumentId = '';

    // Loop through each file document and delete it
    for (const fileDoc of querySnapshot.docs) {
      fileDocumentId = fileDoc.id;
      await fileDoc.ref.delete();
      await deleteAssociatedChats(userId, fileDocumentId);
    }

    // 2. Delete the embeddings from Pinecone
    await deleteVectorsFromPinecone(namespace);

    // 3. Delete the document chunks from Firestore (byteStoreCollection)
    await deleteDocumentChunks(namespace,userId);

    // 4. Delete the file from Firebase Storage
    await deleteFileFromStorage(namespace, userId);

    // 5. Add a notification after the document and associated chats have been deleted
    await addNotification(userId, `Document with namespace: ${namespace} has been successfully removed.`);

    return `Document and associated chats with namespace: ${namespace} have been successfully deleted.`;
  } catch (error) {
    console.error('Error deleting document and chats by namespace:', error);
    throw new Error('Error deleting document and chats.');
  }
};

const deleteVectorsFromPinecone = async (namespace: string) => {
  try {
    const indexName = process.env.PINECONE_INDEX;
    if (!indexName) {
      throw new Error('PINECONE_INDEX environment variable is not set.');
    }

    const index = pineconeClient.Index(indexName);
    await index.namespace(namespace).deleteAll();

    console.log(`Vectors in namespace '${namespace}' have been deleted from Pinecone.`);
  } catch (error) {
    console.error('Error deleting vectors from Pinecone:', error);
    throw new Error('Error deleting vectors from Pinecone.');
  }
};

const deleteAssociatedChats = async (userId: string, fileDocumentId: string) => {
  try {
    const chatsRef = adminDb.collection('users').doc(userId).collection('chats');
    const chatsQuery = chatsRef.where('fileDocumentId', '==', fileDocumentId);
    const chatSnapshot = await chatsQuery.get();

    if (!chatSnapshot.empty) {
      for (const chatDoc of chatSnapshot.docs) {
        await deleteAssociatedMessages(userId, chatDoc.id);
        await chatDoc.ref.delete();
      }
    }
  } catch (error) {
    console.error("Error deleting associated chats:", error);
    throw new Error("Error deleting associated chats.");
  }
};

const deleteAssociatedMessages = async (userId: string, chatId: string) => {
  try {
    const messagesRef = adminDb.collection('users').doc(userId).collection('chats').doc(chatId).collection('messages');
    const messagesSnapshot = await messagesRef.get();
    for (const messageDoc of messagesSnapshot.docs) {
      await messageDoc.ref.delete();
    }
  } catch (error) {
    console.error("Error deleting associated messages:", error);
    throw new Error("Error deleting associated messages.");
  }
};

const deleteDocumentChunks = async (namespace: string, userId: string ) => {
  try {

    const byteCollection = `users/${userId}/byteStoreCollection`;

    const byteStoreCollectionRef = adminDb.collection(byteCollection);
    const chunksQuerySnapshot = await byteStoreCollectionRef.where('metadata.doc_id', '==', namespace).get();

    if (!chunksQuerySnapshot.empty) {
      const batch = adminDb.batch();
      chunksQuerySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      console.log(`Document chunks for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No document chunks found for namespace ${namespace} in Firestore.`);
    }
  } catch (error) {
    console.error('Error deleting document chunks from Firestore:', error);
    throw new Error('Error deleting document chunks from Firestore.');
  }
};

const deleteFileFromStorage = async (namespace: string, userId: string) => {
  try {
    const bucketName = process.env.FIREBASE_STORAGE_BUCKET;
    if (!bucketName) {
      throw new Error('FIREBASE_STORAGE_BUCKET environment variable is not set.');
    }

    const bucket = adminStorage.bucket(bucketName);
    const filePath = `uploads/${userId}/${namespace}`;  // Include user email in the file path

    await bucket.file(filePath).delete();
    console.log(`File ${namespace} deleted from Firebase Storage.`);
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    throw new Error('Error deleting file from Firebase Storage.');
  }
};


const addNotification = async (userId: string, message: string) => {
  try {
    const notificationsRef = adminDb.collection('users').doc(userId).collection('notifications');
    await notificationsRef.add({
      message: message,
      timestamp: new Date(),
      read: false
    });
    console.log(`Notification added for user: ${userId} - ${message}`);
  } catch (error) {
    console.error('Error adding notification:', error);
    throw new Error('Error adding notification.');
  }
};