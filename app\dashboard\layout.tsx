// components/dashboard.tsx

"use client"; // Add this directive to mark as a client component

// Import necessary libraries
import React, { ReactNode } from 'react';
import { AuthProvider } from '../../contexts/AuthContext';
import Header from 'components/Header'; // Adjust the import path as needed

// Define the type for the props
interface DashboardProps {
    children: ReactNode;
}

// Functional component for Dashboard using function keyword
function Dashboard({ children }: DashboardProps) {
    return (
        <AuthProvider>
            <div className="flex flex-col min-h-screen  text-slate-900">
                <Header />
                <main className="flex-1 overflow-y-auto  bg-white text-slate-900">
                    {children}
                </main>
            </div>
        </AuthProvider>
    );
}

export default Dashboard;
