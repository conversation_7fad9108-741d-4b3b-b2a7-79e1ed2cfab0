'use client';

import { useAuth } from '../contexts/AuthContext';

export default function FirebaseAuthTest() {
  const { user, signInWithGoogle, signOut } = useAuth();

  return (
    <div className="p-4 border rounded-lg bg-gray-100 m-4">
      <h3 className="text-lg font-bold mb-4 text-black">Firebase Auth Status</h3>
      
      {user ? (
        <div className="space-y-2">
          <p className="text-green-600 font-semibold">✅ User is signed in!</p>
          <div className="text-black">
            <p><strong>Name:</strong> {user.displayName}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Photo:</strong> {user.photoURL ? '✅ Available' : '❌ Not available'}</p>
            <p><strong>Provider:</strong> {user.providerData[0]?.providerId}</p>
          </div>
          <button
            onClick={signOut}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded mt-2"
          >
            Sign Out
          </button>
        </div>
      ) : (
        <div className="space-y-2">
          <p className="text-red-600 font-semibold">❌ User is not signed in</p>
          <button
            onClick={signInWithGoogle}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            Sign In with Google
          </button>
        </div>
      )}
    </div>
  );
}
