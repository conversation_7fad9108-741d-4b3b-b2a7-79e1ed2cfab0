'use client'

/**
 * @fileoverview SearchHeader Component
 *
 * Purpose:
 * This component serves as the main header for the search interface, providing
 * search functionality, user profile management, and navigation capabilities.
 * It includes a search bar, company branding, user profile display, and sign-out
 * functionality.
 *
 * Features:
 * - Search input with real-time query handling
 * - User profile display and management
 * - Sign out functionality with loading states
 * - Responsive design with mobile considerations
 * - Company branding and navigation
 * - Loading state management
 */

import React, { useState } from 'react';
import { Search } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import LoadingOverlay from 'components/LoadingOverlay';

/**
 * Interface defining the props for the SearchHeader component
 * @property {string} query - Current search query value
 * @property {boolean} isSearching - Flag indicating if a search is in progress
 * @property {function} onQueryChange - Handler for search input changes
 * @property {function} onKeyPress - Handler for keyboard events
 * @property {function} onSearch - Handler for search submission
 */
interface SearchHeaderProps {
  query: string;
  isSearching: boolean;
  onQueryChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onSearch: () => void;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  query,
  isSearching,
  onQueryChange,
  onKeyPress,
  onSearch
}) => {
  // Hooks for authentication and navigation
  const { user, signOut } = useAuth();
  const router = useRouter();

  // Local state management
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  /**
   * Handles the sign out process with proper state management
   * and error handling
   */
  const handleSignOut = async () => {
    // Reset modal state and show loading indicators
    setIsModalOpen(false);
    setIsLoading(true);
    setIsSigningOut(true);

    try {
      // Perform sign out
      await signOut();

      // Clean up UI state and redirect
      setIsModalOpen(false);
      router.push('/');
      router.refresh();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      // Reset loading states regardless of outcome
      setIsLoading(false);
      setIsSigningOut(false);
    }
  };

  return (
    <>
      {/* Global loading overlay */}
      {isLoading && <LoadingOverlay message="Please wait..." />}

      {/* Main header container */}
      <div className="rounded-lg bg-ike-purple border border-ike-purple shadow-lg">
        <div className="p-6">
          {/* Header top section with logo and user profile */}
          <div className="flex items-center justify-between mb-3">
            {/* Company logo and navigation */}
            <div className="flex items-center gap-4">
              <Link href="/fileManager" className="flex items-center -ml-4">
                <Image
                  src="/ikelogoB.png"
                  alt="Company Logo"
                  width={100}
                  height={100}
                  className="hidden sm:block animate-pulse"
                />
              </Link>
            </div>

            {/* User profile section */}
            <div
              className="flex items-center rounded-3xl p-2 shadow-lg shadow-ike-message-bg bg-ike-dark-purple border border-ike-dark-purple cursor-pointer"
              onClick={() => setIsModalOpen(!isModalOpen)}
            >
              <h1 className="text-lg p-2 text-amber-500 font-semi-bold mr-5">Chat Explorer</h1>
              <Image
                src={user?.photoURL || "/default-avatar.png"}
                alt={`${user?.displayName}'s profile`}
                width={40}
                height={40}
                className="rounded-full mr-1 border border-ike-dark-purple"
              />
            </div>
          </div>

          {/* Search description */}
          <p className="text-amber-500">Search through previous chats and documents</p>
        </div>

        {/* User profile modal */}
        {isModalOpen && (
          <div className="absolute top-24 right-8 bg-white p-4 rounded-lg shadow-lg w-64 z-50">
            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={() => setIsModalOpen(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Modal content */}
            <div className="flex flex-col items-center justify-center mt-4">
              <Image
                src={user?.photoURL || "/default-avatar.png"}
                alt="User Profile"
                width={40}
                height={40}
                className="rounded-full mb-2"
              />
              <h2 className="text-md font-semibold text-center">{user?.displayName}</h2>
              <p className="text-sm text-gray-600 text-center mb-4">{user?.email}</p>
              <button
                onClick={handleSignOut}
                className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded w-full transition duration-300 ease-in-out transform hover:scale-105"
              >
                Sign Out
              </button>
            </div>
          </div>
        )}

        {/* Search input section */}
        <div className="p-5 -mt-8">
          <div className="flex gap-2">
            {/* Search input field */}
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Enter your search query..."
                value={query}
                onChange={onQueryChange}
                onKeyPress={onKeyPress}
                className="w-full px-4 py-2 bg-ike-dark-purple border border-ike-purple rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Search button */}
            <button
              onClick={onSearch}
              disabled={isSearching}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
            >
              <Search className="w-5 h-5" />
              {isSearching ? 'Searching...' : 'Search'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchHeader;