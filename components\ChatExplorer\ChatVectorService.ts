import { Pinecone } from '@pinecone-database/pinecone';
import { OpenAIEmbeddings } from "@langchain/openai";
import { createGroqClient } from "lib/llms/groq";
import { ChatGroq } from "@langchain/groq";
import { PineconeStore } from "@langchain/pinecone";
import { ChatData } from 'types';
import type { 
  ChatMessage,  
  ChatVectorContext, 
  ChatInteractionResult, 
  VectorMetadata,
  VectorServiceError
} from './ChatVectorTypes';
import { getHistoricalChats } from './getHistoricalChats';
import _ from 'lodash';
import { db } from 'components/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';


// API Keys and Configuration
const PINECONE_API_KEY = process.env.PINECONE_API_KEY || '';
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
const PINECONE_INDEX = process.env.PINECONE_INDEX || '';

// Default category for when none is provided
const DEFAULT_CATEGORY = 'Unknown';

// Interface definitions
interface HistoricalChat {
  userMessage: ChatData;
  aiMessage: ChatData;
  context: ChatVectorContext;
}

interface SyncResult {
  successCount: number;
  failureCount: number;
  errors: Array<{
    chatId: string;
    error: string;
  }>;
  status: string;
}

// Type definition for ProgressCallback
export interface ProgressCallback {
  (progress: {
    totalChats: number;
    processedChats: number;
    currentChatId: string;
    messagesFound: number;
    chatPairsFound: number;
  }): void;
}

// Updated type definition
interface TimeStamp {
  createdAt: { seconds: number; nanoseconds: number }; // Updated to match the expected structure
  timestamp: number;
}


// Add this new interface
interface FileDocument {
  name: string;
  fileDocumentId: string;
  // Add other fields as needed
}

// Add this helper function
const getFileNameFromFirestore = async (
  userEmail: string, 
  fileDocumentId: string
): Promise<string> => {
  if (!fileDocumentId) return '';
  
  try {
    const filesRef = collection(db, 'users', userEmail, 'files');
    const q = query(filesRef, where('namespace', '==', fileDocumentId));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.warn(`No file found for fileDocumentId: ${fileDocumentId}`);
      return '';
    }
    
    const fileDoc = querySnapshot.docs[0].data() as FileDocument;
    return fileDoc.name || '';
  } catch (error) {
    console.error('Error fetching filename from Firestore:', error);
    return '';
  }
};


// Simple timestamp function
const createTimestamp = (): TimeStamp => {
  const now = Date.now();
  return {
    createdAt: { seconds: Math.floor(now / 1000), nanoseconds: (now % 1000) * 1e6 },
    timestamp: now
  };
};

// Main function to create the vector service
export const createVectorService = async (userEmail: string) => {
  // Input validation
  if (!userEmail) {
    const error = new Error("User email is required") as VectorServiceError;
    error.status = 400;
    throw error;
  }

  // Initialize services
  const groqClient = createGroqClient({ userEmail });
  const pinecone = new Pinecone({ apiKey: PINECONE_API_KEY });
  const embeddings = new OpenAIEmbeddings({ apiKey: OPENAI_API_KEY });
  const index = pinecone.index(PINECONE_INDEX);

  // Helper function to generate namespace name
  const generateNamespace = (email: string): string => {
    return `${email}_chats`;
  };

  // Helper function to check namespace existence
  const namespaceExists = async (namespace: string): Promise<boolean> => {
    if (!namespace) {
      const error = new Error("Namespace was not provided") as VectorServiceError;
      error.status = 400;
      throw error;
    }

    try {
      const { namespaces } = await index.describeIndexStats();
      const exists = namespaces?.[namespace] !== undefined;
      console.log(`Checked namespace ${namespace}, exists: ${exists}`);
      return exists;
    } catch (error) {
      console.error(`Error checking namespace existence:`, error);
      return false;
    }
  };

  // Function to create embeddings from chat messages
  const createEmbedding = async (userMessage: string, aiResponse: string): Promise<number[]> => {
    try {
      const combinedText = `User: ${userMessage}\nAI: ${aiResponse}`;
      const embedding = await embeddings.embedQuery(combinedText);
      console.log('Successfully created embedding');
      return embedding;
    } catch (error) {
      console.error('Error creating embedding:', error);
      const vectorError = new Error('Failed to create embedding') as VectorServiceError;
      vectorError.status = 500;
      throw vectorError;
    }
  };

  // Function to generate summary of chat interaction
  const generateSummary = async (userMessage: string, aiResponse: string): Promise<string> => {
    try {
      const model = new ChatGroq({
        temperature: 0.2,
        model: process.env.GROQ_MODEL,
        apiKey: groqClient.apiKey!,
      });
  
      const response = await model.invoke([
        {
          role: "system",
          content: `Present the discussion points in a concise flowing paragraph 
          format, omitting any introductory phrases or sentences.`
        },
        {
          role: "user",
          content: `User message: ${userMessage}\nAI response: ${aiResponse}`
        },
      ]);

      const summary = typeof response.content === 'string' ? response.content : "Chat interaction summary";
      console.log('Generated summary:', summary);
      return summary;
    } catch (error) {
      console.error("Error generating summary:", error);
      return "Chat interaction summary";
    }
  };

  // Helper function to format chat messages
  const formatChatMessage = (message: ChatData): ChatMessage => {
    const { createdAt } = createTimestamp();
    return {
      id: message.id,
      text: message.text,
      role: message.role,
      createdAt,
      fileDocumentId: message.fileDocumentId ?? '',
      fileName: message.fileName ?? '',
      category: message.category ?? DEFAULT_CATEGORY,
      userId: message.userId ?? ''
    };
  };

  // Function to process a batch of historical chats with retry logic
  const processBatchWithRetry = async (
    batch: HistoricalChat[],
    store: PineconeStore,
    namespace: string,
    embeddings: OpenAIEmbeddings,
    model: ChatGroq,
    maxRetries = 3
  ): Promise<{
    successful: string[];
    failed: Array<{ chatId: string; error: string }>;
  }> => {
    const result = {
      successful: [] as string[],
      failed: [] as Array<{ chatId: string; error: string }>
    };
  
    try {
      // 1. Create embeddings for all messages in batch
      const chatTexts = batch.map(chat => ({
        text: `User: ${chat.userMessage.text}\nAI: ${chat.aiMessage.text}`,
        chatId: chat.context.selectedDocId
      }));
  
      let embeddings_array: number[][] = [];
      let attempt = 0;
      let success = false;
  
      while (!success && attempt < maxRetries) {
        try {
          embeddings_array = await Promise.all(
            chatTexts.map(async ({ text }) => embeddings.embedQuery(text))
          );
          success = true;
        } catch (error) {
          attempt++;
          if (attempt === maxRetries) {
            throw error;
          }
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
  
      // 2. Generate summaries
      const summaries = await Promise.all(
        batch.map(async chat => {
          try {
            const response = await model.invoke([
              {
                role: "system",
                content: "Shape these discussion points into a concise flowing paragraph."
              },
              {
                role: "user",
                content: `User message: ${chat.userMessage.text}\nAI response: ${chat.aiMessage.text}`
              }
            ]);
            return typeof response.content === 'string' ? response.content : "Chat interaction summary";
          } catch (error) {
            console.error('Error generating summary:', error);
            return "Chat interaction summary";
          }
        })
      );
  
      // 3. Prepare vectors for Pinecone
      const vectors = batch.map((chat, index) => {
        const metadata = {
          namespace,
          text: chat.userMessage.text,
          userId: chat.userMessage.userId,
          role: chat.userMessage.role,
          createdAt: createTimestamp().timestamp,
          fileDocumentId: chat.userMessage.fileDocumentId || '',
          fileName: chat.userMessage.fileName || '',
          category: chat.context.category || DEFAULT_CATEGORY,
          summary: summaries[index],
          documentNamespace: chat.context.documentNamespace,
          chatId: chat.context.selectedDocId,
          userMessageId: chat.userMessage.id,
          aiMessageId: chat.aiMessage.id,
          timestamp: createTimestamp().timestamp,
        };
  
        return {
          vector: embeddings_array[index],
          metadata,
          id: `${chat.context.selectedDocId}-${chat.userMessage.id}-${chat.aiMessage.id}`
        };
      });
  
      // 4. Upsert to Pinecone with retry logic
      attempt = 0;
      success = false;
  
      while (!success && attempt < maxRetries) {
        try {
          await store.addVectors(
            vectors.map(v => v.vector),
            vectors.map(v => ({
              pageContent: `${v.metadata.userMessageId}-${v.metadata.aiMessageId}`,
              metadata: v.metadata
            }))
          );
          success = true;
          result.successful.push(...batch.map(chat => chat.context.selectedDocId));
        } catch (error) {
          attempt++;
          if (attempt === maxRetries) {
            throw error;
          }
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
  
    } catch (error) {
      console.error('Error processing batch:', error);
      batch.forEach(chat => {
        result.failed.push({
          chatId: chat.context.selectedDocId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      });
    }
  
    return result;
  };

  // Function to upsert vector with namespace handling
  const upsertVectorWithNamespace = async (
    vectorId: string,
    embedding: number[],
    metadata: Omit<VectorMetadata, 'namespace'>,
    chatsNamespace: string
  ): Promise<boolean> => {
    try {
      if (!chatsNamespace) {
        const error = new Error("Namespace was not provided") as VectorServiceError;
        error.status = 400;
        throw error;
      }

      const exists = await namespaceExists(chatsNamespace);
      console.log(`Preparing to upsert to namespace ${chatsNamespace} (exists: ${exists})`);

      const store = new PineconeStore(embeddings, {
        pineconeIndex: index,
        namespace: chatsNamespace,
      });

      const fullMetadata: VectorMetadata = {
        ...metadata,
        namespace: chatsNamespace,
        category: metadata.category || '',
        summary: metadata.summary || '',
        documentNamespace: metadata.documentNamespace || '',
        chatId: metadata.chatId || '',
        userMessageId: metadata.userMessageId || '',
        aiMessageId: metadata.aiMessageId || '',
        timestamp: metadata.timestamp || 0,
        fileDocumentId: metadata.fileDocumentId || ''
      };

      await store.addVectors(
        [embedding],
        [{
          pageContent: `${metadata.userMessageId}-${metadata.aiMessageId}`,
          metadata: fullMetadata
        }]
      );

      console.log(`Successfully upserted to chat namespace ${chatsNamespace}`);
      console.log(`Successfully upserted metadata`, fullMetadata);
      return true;
    } catch (error) {
      console.error('Error upserting vector:', error);
      const vectorError = new Error('Failed to upsert vector') as VectorServiceError;
      vectorError.status = 500;
      throw vectorError;
    }
  };

  // Main function to save chat interaction
  const saveChatInteraction = async (
    userMessage: ChatData,
    aiMessage: ChatData,
    context: ChatVectorContext,
    onProgress?: ProgressCallback
  ): Promise<ChatInteractionResult> => {
    const category = context.category || DEFAULT_CATEGORY;
    console.log(`Using category: ${category} ${context.category ? '(provided)' : '(default)'}`);
  
    const requiredFields = {
      userEmail,
      selectedDocId: context.selectedDocId,
      documentNamespace: context.documentNamespace,
      category: context.category,
      userMessage,
      aiMessage
    };
  
    for (const [field, value] of Object.entries(requiredFields)) {
      if (!value) {
        const error = new Error(`Missing required field: ${field}`) as VectorServiceError;
        error.status = 400;
        throw error;
      }
    }
  
    try {
      console.log('Starting chat interaction save process...');
      
      // Check namespace existence first
      const chatsNamespace = generateNamespace(userEmail);
      const namespaceAlreadyExists = await namespaceExists(chatsNamespace);
  
      // If namespace doesn't exist, process historical chats first
      if (!namespaceAlreadyExists) {
        console.log('Namespace does not exist, initiating historical chat processing...');
        try {
          // Get historical chats
          const historicalChats = await getHistoricalChats(userEmail, onProgress);
          console.log(`Found ${historicalChats.length} historical chats to process`);
  
          if (historicalChats.length > 0) {
            // Process historical chats in batches
            const syncResult = await syncHistoricalChats(historicalChats);
            console.log('Historical chat sync result:', syncResult);
  
            if (syncResult.status === 'completed_with_errors') {
              console.warn('Historical sync completed with errors:', syncResult.errors);
            } else {
              console.log('Historical sync completed successfully');
            }
          } else {
            console.log('No historical chats found to process');
          }
        } catch (error) {
          console.error('Error processing historical chats:', error);
          // Continue with current message even if historical processing fails
        }
      }
  
      // Process current message
      const userMessageForVector = formatChatMessage(userMessage);
      const aiMessageForVector = formatChatMessage(aiMessage);
      
      // Create embedding for the current message pair
      console.log('Creating embedding for current message...');
      const embedding = await createEmbedding(userMessage.text, aiMessage.text);
      
      // Generate summary for the current message pair
      console.log('Generating summary for current message...');
      const summary = await generateSummary(userMessage.text, aiMessage.text);

      const fileName = await getFileNameFromFirestore(userEmail, userMessageForVector.fileDocumentId);
      console.log('Retrieved filename from Firestore:', fileName);
      
  
      // Prepare metadata for vector storage
      const { createdAt, timestamp } = createTimestamp();
      const metadata: Omit<VectorMetadata, 'namespace'> = {
        text: userMessage.text,
        userId: userEmail,
        role: userMessage.role,
        createdAt,
        fileDocumentId: userMessageForVector.fileDocumentId,
        fileName: fileName,
        category,
        summary,
        documentNamespace: userMessageForVector.fileDocumentId,
        chatId: context.selectedDocId,
        userMessageId: userMessageForVector.id,
        aiMessageId: aiMessageForVector.id,
        timestamp
      };
  
      // Generate unique vector ID for the message pair
      const vectorId = `${context.selectedDocId}-${userMessageForVector.id}-${aiMessageForVector.id}`;
      
      // Upsert the vector to Pinecone
      console.log('Upserting vector to namespace:', chatsNamespace);
      await upsertVectorWithNamespace(vectorId, embedding, metadata, chatsNamespace);
  
      // Return success result
      return { 
        vectorId,
        namespace: chatsNamespace,
        status: namespaceAlreadyExists 
          ? "Embeddings upserted into existing namespace." 
          : "New namespace created and all historical chats processed successfully."
      };
  
    } catch (error) {
      console.error('Error saving chat interaction:', error);
      
      // Enhance error handling with specific status codes
      const vectorError = new Error(
        error instanceof Error ? error.message : 'Failed to save chat interaction'
      ) as VectorServiceError;
      
      // Set appropriate status code based on error type
      if (error instanceof Error && error.message.includes('rate limit')) {
        vectorError.status = 429; // Too Many Requests
      } else if (error instanceof Error && error.message.includes('unauthorized')) {
        vectorError.status = 401; // Unauthorized
      } else {
        vectorError.status = (error as VectorServiceError).status || 500;
      }
      
      throw vectorError;
    } finally {
      // Clean up any progress tracking if necessary
      if (onProgress) {
        onProgress({
          totalChats: 0,
          processedChats: 0,
          currentChatId: '',
          messagesFound: 0,
          chatPairsFound: 0
        });
      }
    }
  };

  // Function to sync historical chats
  const syncHistoricalChats = async (
    historicalChats: HistoricalChat[],
    onProgress?: ProgressCallback
  ): Promise<SyncResult> => {
    if (!historicalChats?.length) {
      return { successCount: 0, failureCount: 0, errors: [], status: 'completed' };
    }
  
    console.log(`Starting historical chat sync for ${historicalChats.length} chats`);
  
    const result: SyncResult = {
      successCount: 0,
      failureCount: 0,
      errors: [],
      status: 'processing'
    };
  
    try {
      // Initialize services
      const namespace = generateNamespace(userEmail);
      const store = new PineconeStore(embeddings, {
        pineconeIndex: index,
        namespace,
      });
  
      const model = new ChatGroq({
        temperature: 0.2,
        model: process.env.GROQ_MODEL,
        apiKey: groqClient.apiKey!,
      });
  
      // Process in smaller batches
      const BATCH_SIZE = 5;
      const batches = _.chunk(historicalChats, BATCH_SIZE);
  
      let processedCount = 0;
      for (const batch of batches) {
        const batchResult = await processBatchWithRetry(
          batch,
          store,
          namespace,
          embeddings,
          model
        );
  
        // Update results
        result.successCount += batchResult.successful.length;
        result.failureCount += batchResult.failed.length;
        result.errors.push(...batchResult.failed);
  
        // Update progress
        processedCount += batch.length;
        if (onProgress) {
          onProgress({
            totalChats: historicalChats.length,
            processedChats: processedCount,
            currentChatId: batch[batch.length - 1]?.context?.selectedDocId || '',
            messagesFound: processedCount,
            chatPairsFound: result.successCount
          });
        }
  
        // Add delay between batches
        if (batches.indexOf(batch) !== batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
  
      result.status = result.failureCount === 0 ? 'completed' : 'completed_with_errors';
  
    } catch (error) {
      console.error('Error in sync process:', error);
      result.status = 'failed';
      result.errors.push({
        chatId: 'sync-process',
        error: error instanceof Error ? error.message : 'Unknown sync error'
      });
    }
  
    console.log('Historical chat sync completed:', {
      total: historicalChats.length,
      successful: result.successCount,
      failed: result.failureCount,
      errorCount: result.errors.length
    });
  
    return result;
  };

  // Return the public interface of the service
  return {
    saveChatInteraction,
    namespaceExists,
    generateNamespace,
    syncHistoricalChats
  };
};

// This is a placeholder to satisfy the React component requirement
export default function Component() {
  return null;
}