'use client'

import React, { useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import SearchInterface from 'components/ChatExplorer/SearchInterface';

export const dynamic = 'force-dynamic';

function ChatSearchPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [loading, user, router]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null;
  }

  return (
    <div>
      <SearchInterface />
    </div>
  );
}

export default ChatSearchPage;