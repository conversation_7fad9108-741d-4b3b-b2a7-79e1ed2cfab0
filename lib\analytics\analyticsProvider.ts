'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { AnalyticsMiddleware } from './AnalyticsMiddleware';
import { initializeFirebase } from 'components/firebase';

interface AnalyticsEventData {
  namespaces?: string[];
  query?: string;
  chatHistory?: string;
  category?: string;
  [key: string]: any;
}

interface UserWithId {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

interface AnalyticsContextType {
  analyticsMiddleware: AnalyticsMiddleware | null;
  isInitialized: boolean;
  error: Error | null;
  trackEvent: (eventName: string, eventData: AnalyticsEventData) => Promise<void>;
}

interface AnalyticsState {
  analyticsMiddleware: AnalyticsMiddleware | null;
  isInitialized: boolean;
  error: Error | null;
}

const defaultContextValue: AnalyticsContextType = {
  analyticsMiddleware: null,
  isInitialized: false,
  error: null,
  trackEvent: async () => {}
};

const AnalyticsContext = createContext<AnalyticsContextType>(defaultContextValue);

export function AnalyticsProvider({ children }: { children: ReactNode }): JSX.Element {
  const { user, loading } = useAuth();
  const [state, setState] = useState<AnalyticsState>({
    analyticsMiddleware: null,
    isInitialized: false,
    error: null
  });

  useEffect(() => {
    let mounted = true;

    const initAnalytics = async () => {
      const userId = user?.uid;

      if (!userId) {
        return;
      }

      try {
        initializeFirebase();
        const middleware = new AnalyticsMiddleware();

        if (mounted) {
          setState({
            analyticsMiddleware: middleware,
            isInitialized: true,
            error: null
          });
        }
      } catch (error) {
        console.error('Failed to initialize analytics:', error);
        if (mounted) {
          setState(prev => ({
            ...prev,
            error: error instanceof Error ? error : new Error('Failed to initialize analytics'),
            isInitialized: true
          }));
        }
      }
    };

    if (user) {
      initAnalytics();
    }

    return () => {
      mounted = false;
    };
  }, [user]);

  const trackEvent = useCallback(async (eventName: string, eventData: AnalyticsEventData): Promise<void> => {
    if (!state.analyticsMiddleware || !user) {
      return;
    }

    try {
      await state.analyticsMiddleware.interceptQueryStart(
        user.uid,
        eventData.namespaces || null,
        eventData.query || '',
        eventData.chatHistory || '',
        eventData.category || null
      );
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  }, [state.analyticsMiddleware, user]);

  // Handle loading state
  if (loading) {
    return React.createElement(
      'div',
      { className: 'flex items-center justify-center p-4' },
      React.createElement(
        'div',
        { className: 'space-y-2' },
        React.createElement('div', {
          className: 'h-4 w-24 bg-gray-200 animate-pulse rounded'
        }),
        React.createElement('div', {
          className: 'h-4 w-32 bg-gray-200 animate-pulse rounded'
        })
      )
    );
  }

  // Handle unauthenticated state
  if (!user) {
    return React.createElement(React.Fragment, null, children);
  }

  // Handle error state
  if (state.error) {
    console.error('Analytics initialization failed:', state.error);
    return React.createElement(React.Fragment, null, children);
  }

  // Create context value
  const contextValue: AnalyticsContextType = {
    ...state,
    trackEvent
  };

  // Return provider with context value
  return React.createElement(
    AnalyticsContext.Provider,
    { value: contextValue },
    children
  );
}

export const useAnalytics = (): AnalyticsContextType => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};